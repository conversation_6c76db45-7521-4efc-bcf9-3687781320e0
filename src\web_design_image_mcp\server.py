"""
Main MCP server implementation for web design image generation.
"""

import logging
import sys
from contextlib import asynccontextmanager
from typing import Async<PERSON><PERSON><PERSON>
from dataclasses import dataclass

from mcp.server.fastmcp import FastMCP, Context
from .config import config
from .services import ModelScopeClient, PromptGenerator, ImageProcessor, WebContextAnalyzer
from .services.prompt_generator import ImageType, WebsiteCategory, ImageContext
from .services.context_analyzer import WebPageContext


# Configure logging
logging.basicConfig(
    level=getattr(logging, config.log_level.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("mcp_server.log") if config.debug else logging.NullHandler()
    ]
)

logger = logging.getLogger(__name__)


@dataclass
class ServerContext:
    """Server context for managing resources and state."""
    cache_dir: str
    api_client: ModelScopeClient = None
    prompt_generator: PromptGenerator = None
    image_processor: ImageProcessor = None
    context_analyzer: WebContextAnalyzer = None


@asynccontextmanager
async def server_lifespan(server: FastMCP) -> AsyncIterator[ServerContext]:
    """Manage server startup and shutdown lifecycle."""
    logger.info(f"Starting {config.server_name} v{config.server_version}")

    # Initialize server context
    context = ServerContext(cache_dir=config.cache_dir)

    try:
        # Initialize services
        context.api_client = ModelScopeClient()
        context.prompt_generator = PromptGenerator()
        context.image_processor = ImageProcessor()
        context.context_analyzer = WebContextAnalyzer()

        logger.info("Server initialization complete")
        yield context

    except Exception as e:
        logger.error(f"Error during server initialization: {e}")
        raise
    finally:
        # Cleanup resources
        logger.info("Shutting down server")


def create_server() -> FastMCP:
    """Create and configure the MCP server."""
    mcp = FastMCP(
        name=config.server_name,
        dependencies=["requests", "Pillow", "pydantic", "beautifulsoup4", "httpx", "aiofiles"],
        lifespan=server_lifespan
    )

    # Note: Error handling is done within individual tool functions

    # Add MCP tools
    _add_image_generation_tools(mcp)
    _add_context_analysis_tools(mcp)
    _add_prompt_tools(mcp)

    # Add MCP resources
    _add_resources(mcp)

    return mcp


def main():
    """Main entry point for the server."""
    try:
        server = create_server()
        logger.info("Starting MCP server...")
        server.run()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server failed to start: {e}", exc_info=True)
        sys.exit(1)


def _add_image_generation_tools(mcp: FastMCP):
    """Add image generation tools to the MCP server."""

    @mcp.tool()
    async def generate_web_image(
        prompt: str,
        image_type: str = "photo",
        website_category: str = "business",
        width: int = 1024,
        height: int = 1024,
        format: str = "webp",
        steps: int = None,
        guidance_scale: float = None,
        api_key: str = None,
        ctx: Context = None
    ) -> dict:
        """Generate an image for web design use.

        Args:
            prompt: Text description of the desired image
            image_type: Type of image (hero, product, background, icon, etc.)
            website_category: Category of website (business, ecommerce, blog, etc.)
            width: Image width in pixels
            height: Image height in pixels
            format: Output format (webp, png, jpeg, avif)
            steps: Number of inference steps (optional)
            guidance_scale: Guidance scale for generation (optional)
            api_key: ModelScope API key (required)

        Returns:
            Dictionary with image file path, metadata, and base64 data
        """
        if not api_key:
            raise ValueError("API key is required for image generation")

        # Get server context
        server_ctx = mcp.get_context().request_context.lifespan_context

        try:
            # Parse enum values
            img_type = ImageType(image_type.lower())
            web_category = WebsiteCategory(website_category.lower())
        except ValueError as e:
            raise ValueError(f"Invalid type or category: {e}")

        # Create image context
        image_context = ImageContext(
            image_type=img_type,
            website_category=web_category,
            content_description=prompt,
            style_keywords=[],
            technical_specs={'width': width, 'height': height}
        )

        # Generate enhanced prompt
        enhanced_prompt = server_ctx.prompt_generator.generate_prompt(image_context)

        await ctx.info(f"Generating image with prompt: {enhanced_prompt[:100]}...")

        # Generate image
        result = await server_ctx.api_client.generate_image(
            prompt=enhanced_prompt,
            width=width,
            height=height,
            steps=steps,
            guidance_scale=guidance_scale,
            api_key=api_key
        )

        # Process image
        processed = await server_ctx.image_processor.process_image(
            image_data=result.image_data,
            target_format=format,
            target_size=(width, height),
            include_base64=True
        )

        await ctx.info(f"Image generated successfully: {processed.file_path}")

        return {
            "file_path": processed.file_path,
            "format": processed.format,
            "size": processed.size,
            "file_size": processed.file_size,
            "base64_data": processed.base64_data,
            "prompt_used": enhanced_prompt,
            "generation_time": result.generation_time.isoformat(),
            "metadata": result.metadata
        }


def _add_context_analysis_tools(mcp: FastMCP):
    """Add context analysis tools to the MCP server."""

    @mcp.tool()
    async def analyze_web_context(
        html_content: str,
        css_content: str = "",
        ctx: Context = None
    ) -> dict:
        """Analyze web page context to understand image requirements.

        Args:
            html_content: HTML content of the web page
            css_content: CSS content (optional)

        Returns:
            Dictionary with page context and image placeholder information
        """
        # Get server context
        server_ctx = mcp.get_context().request_context.lifespan_context

        await ctx.info("Analyzing web page context...")

        # Analyze the page
        page_context = server_ctx.context_analyzer.analyze_html(html_content, css_content)

        # Convert to serializable format
        result = {
            "title": page_context.title,
            "description": page_context.description,
            "content_type": page_context.content_type.value,
            "color_scheme": page_context.color_scheme,
            "style_attributes": page_context.style_attributes,
            "semantic_sections": page_context.semantic_sections,
            "image_placeholders": [
                {
                    "element_type": p.element_type,
                    "alt_text": p.alt_text,
                    "class_names": p.class_names,
                    "context_text": p.context_text,
                    "suggested_type": p.suggested_type.value,
                    "dimensions": p.dimensions
                }
                for p in page_context.image_placeholders
            ]
        }

        await ctx.info(f"Found {len(page_context.image_placeholders)} image placeholders")

        return result


def _add_prompt_tools(mcp: FastMCP):
    """Add prompt generation tools to the MCP server."""

    @mcp.tool()
    async def suggest_image_prompts(
        content_description: str,
        image_type: str = "photo",
        website_category: str = "business",
        style_keywords: list = None,
        color_scheme: list = None,
        ctx: Context = None
    ) -> dict:
        """Suggest optimized prompts for image generation.

        Args:
            content_description: Description of the desired image content
            image_type: Type of image (hero, product, background, etc.)
            website_category: Category of website (business, ecommerce, etc.)
            style_keywords: List of style keywords
            color_scheme: List of colors (hex codes or names)

        Returns:
            Dictionary with suggested prompts and variations
        """
        # Get server context
        server_ctx = mcp.get_context().request_context.lifespan_context

        try:
            # Parse enum values
            img_type = ImageType(image_type.lower())
            web_category = WebsiteCategory(website_category.lower())
        except ValueError as e:
            raise ValueError(f"Invalid type or category: {e}")

        # Create image context
        image_context = ImageContext(
            image_type=img_type,
            website_category=web_category,
            content_description=content_description,
            style_keywords=style_keywords or [],
            color_scheme=color_scheme or []
        )

        # Generate main prompt
        main_prompt = server_ctx.prompt_generator.generate_prompt(image_context)

        # Generate variations with different styles
        variations = []
        style_variations = [
            ["minimal", "clean"],
            ["dramatic", "cinematic"],
            ["modern", "sleek"],
            ["artistic", "creative"]
        ]

        for styles in style_variations:
            variant_context = ImageContext(
                image_type=img_type,
                website_category=web_category,
                content_description=content_description,
                style_keywords=styles,
                color_scheme=color_scheme or []
            )
            variant_prompt = server_ctx.prompt_generator.generate_prompt(variant_context)
            variations.append({
                "style": styles,
                "prompt": variant_prompt
            })

        return {
            "main_prompt": main_prompt,
            "variations": variations,
            "image_type": img_type.value,
            "website_category": web_category.value
        }


def _add_resources(mcp: FastMCP):
    """Add MCP resources to the server."""

    @mcp.resource("templates://image-types")
    def get_image_types() -> str:
        """Get available image types for web design."""
        types = [
            {
                "type": "hero",
                "description": "Large banner images for website headers",
                "typical_size": "1920x1080",
                "use_cases": ["landing pages", "website headers", "promotional banners"]
            },
            {
                "type": "product",
                "description": "Product photography for e-commerce",
                "typical_size": "800x800",
                "use_cases": ["product catalogs", "shopping pages", "item listings"]
            },
            {
                "type": "background",
                "description": "Subtle background patterns and textures",
                "typical_size": "1920x1080",
                "use_cases": ["page backgrounds", "section dividers", "texture overlays"]
            },
            {
                "type": "icon",
                "description": "Simple symbolic graphics",
                "typical_size": "64x64",
                "use_cases": ["navigation", "feature highlights", "service icons"]
            },
            {
                "type": "avatar",
                "description": "Profile pictures and user representations",
                "typical_size": "200x200",
                "use_cases": ["user profiles", "team pages", "testimonials"]
            }
        ]

        import json
        return json.dumps(types, indent=2)

    @mcp.resource("templates://website-categories")
    def get_website_categories() -> str:
        """Get available website categories."""
        categories = [
            {
                "category": "business",
                "description": "Corporate and professional websites",
                "style_keywords": ["professional", "trustworthy", "corporate", "clean"]
            },
            {
                "category": "ecommerce",
                "description": "Online stores and shopping websites",
                "style_keywords": ["product-focused", "clean", "commercial", "appealing"]
            },
            {
                "category": "creative",
                "description": "Artist portfolios and creative agencies",
                "style_keywords": ["artistic", "unique", "expressive", "bold"]
            },
            {
                "category": "tech",
                "description": "Technology and software companies",
                "style_keywords": ["modern", "innovative", "digital", "sleek"]
            },
            {
                "category": "health",
                "description": "Healthcare and wellness websites",
                "style_keywords": ["clean", "caring", "natural", "calming"]
            }
        ]

        import json
        return json.dumps(categories, indent=2)

    @mcp.resource("examples://prompts/{image_type}")
    def get_prompt_examples(image_type: str) -> str:
        """Get example prompts for different image types."""
        examples = {
            "hero": [
                "stunning mountain landscape at sunrise, cinematic lighting, professional photography",
                "modern office building with glass facade, architectural photography, clean lines",
                "diverse team collaborating in bright workspace, corporate photography, natural lighting"
            ],
            "product": [
                "elegant smartphone on white background, studio lighting, commercial photography",
                "artisanal coffee cup with steam, warm lighting, product photography",
                "luxury watch with leather strap, macro photography, high detail"
            ],
            "background": [
                "subtle geometric pattern in soft blue tones, minimal design, seamless texture",
                "abstract watercolor wash in pastel colors, artistic background, soft gradients",
                "clean white marble texture with subtle veining, elegant background"
            ],
            "icon": [
                "simple house icon, flat design, minimal style, geometric shapes",
                "email envelope symbol, clean lines, professional icon design",
                "shopping cart icon, modern style, clear symbolism"
            ]
        }

        type_examples = examples.get(image_type.lower(), ["No examples available for this type"])

        import json
        return json.dumps({
            "image_type": image_type,
            "examples": type_examples,
            "tips": [
                "Be specific about style and mood",
                "Include lighting and quality keywords",
                "Mention the intended use context",
                "Add technical specifications if needed"
            ]
        }, indent=2)

    @mcp.resource("config://server-info")
    def get_server_info() -> str:
        """Get server configuration and capabilities."""
        info = {
            "server_name": config.server_name,
            "server_version": config.server_version,
            "supported_formats": config.supported_formats,
            "default_image_size": f"{config.default_image_width}x{config.default_image_height}",
            "max_image_size": config.max_image_size,
            "cache_enabled": config.cache_enabled,
            "rate_limits": {
                "max_requests_per_minute": config.max_requests_per_minute,
                "max_concurrent_requests": config.max_concurrent_requests
            },
            "capabilities": [
                "Context-aware image generation",
                "Web page analysis",
                "Intelligent prompt generation",
                "Multiple output formats",
                "Image optimization",
                "Caching system"
            ]
        }

        import json
        return json.dumps(info, indent=2)


# For direct execution
if __name__ == "__main__":
    main()
